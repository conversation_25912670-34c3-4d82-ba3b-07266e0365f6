// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'signup_login_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$phoneNumberHash() => r'e8b3ed5eaa403e5e32b30db47e5bc220f7a2b07c';

/// Provider for phone number
///
/// Copied from [phoneNumber].
@ProviderFor(phoneNumber)
final phoneNumberProvider = AutoDisposeProvider<String>.internal(
  phoneNumber,
  name: r'phoneNumberProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$phoneNumberHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PhoneNumberRef = AutoDisposeProviderRef<String>;
String _$isLoggingInHash() => r'a4ae743623d1a1c5257e710187acb3ab57817b7b';

/// Provider for loading state
///
/// Copied from [isLoggingIn].
@ProviderFor(isLoggingIn)
final isLoggingInProvider = AutoDisposeProvider<bool>.internal(
  isLoggingIn,
  name: r'isLoggingInProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isLoggingInHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsLoggingInRef = AutoDisposeProviderRef<bool>;
String _$isMobileEnabledHash() => r'93456eae55849cd52179c77ccaf8fd2d1d10b8a1';

/// Provider for mobile field enabled state
///
/// Copied from [isMobileEnabled].
@ProviderFor(isMobileEnabled)
final isMobileEnabledProvider = AutoDisposeProvider<bool>.internal(
  isMobileEnabled,
  name: r'isMobileEnabledProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isMobileEnabledHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsMobileEnabledRef = AutoDisposeProviderRef<bool>;
String _$countryCodeHash() => r'0a05f049832c4547c00ab81c941c481f4dc76fef';

/// Provider for country code
///
/// Copied from [countryCode].
@ProviderFor(countryCode)
final countryCodeProvider = AutoDisposeProvider<String>.internal(
  countryCode,
  name: r'countryCodeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$countryCodeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CountryCodeRef = AutoDisposeProviderRef<String>;
String _$errorMessageHash() => r'76a65f7d694c30bba44875ef04e9c2d6ec41cf98';

/// Provider for error message
///
/// Copied from [errorMessage].
@ProviderFor(errorMessage)
final errorMessageProvider = AutoDisposeProvider<String?>.internal(
  errorMessage,
  name: r'errorMessageProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$errorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef ErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$signupLoginHash() => r'4b863891099d3c6a45ba78536f0d482e57e76692';

/// Signup Login Notifier
///
/// Manages state and business logic for the signup/login screen
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [SignupLogin].
@ProviderFor(SignupLogin)
final signupLoginProvider =
    AutoDisposeNotifierProvider<SignupLogin, SignupLoginState>.internal(
  SignupLogin.new,
  name: r'signupLoginProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$signupLoginHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SignupLogin = AutoDisposeNotifier<SignupLoginState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
