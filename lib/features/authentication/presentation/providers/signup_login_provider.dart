/// Signup Login Provider
///
/// Provides Riverpod state management for signup/login screen
/// Replaces SignupLoginViewModel with Riverpod architecture
library signup_login_provider;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/features/authentication/presentation/providers/auth_usecase_provider.dart';
import 'package:towasl/features/authentication/domain/validators/auth_validators.dart';
import 'package:towasl/shared/widgets/toasts_custom.dart';

part 'signup_login_provider.g.dart';

/// State class for signup/login screen
class SignupLoginState {
  /// Current phone number value
  final String phoneNumber;

  /// Whether login is in progress
  final bool isLoggingIn;

  /// Whether the mobile field is enabled
  final bool isMobileEnabled;

  /// Current country code (Saudi Arabia)
  final String countryCode;

  /// Error message if any
  final String? errorMessage;

  const SignupLoginState({
    this.phoneNumber = '',
    this.isLoggingIn = false,
    this.isMobileEnabled = true,
    this.countryCode = '966',
    this.errorMessage,
  });

  SignupLoginState copyWith({
    String? phoneNumber,
    bool? isLoggingIn,
    bool? isMobileEnabled,
    String? countryCode,
    String? errorMessage,
    bool clearErrorMessage = false,
  }) {
    return SignupLoginState(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isLoggingIn: isLoggingIn ?? this.isLoggingIn,
      isMobileEnabled: isMobileEnabled ?? this.isMobileEnabled,
      countryCode: countryCode ?? this.countryCode,
      errorMessage:
          clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
    );
  }

  /// Clear error message
  SignupLoginState clearError() {
    if (kDebugMode) {
      print(
          'SignupLoginState: clearError() called, current error: $errorMessage');
    }
    final newState = copyWith(clearErrorMessage: true);
    if (kDebugMode) {
      print(
          'SignupLoginState: clearError() result, new error: ${newState.errorMessage}');
    }
    return newState;
  }
}

/// Signup Login Notifier
///
/// Manages state and business logic for the signup/login screen
/// Follows MVVM pattern with Riverpod state management
@riverpod
class SignupLogin extends _$SignupLogin {
  /// Text controller for mobile number input
  late final TextEditingController mobileController;

  /// Focus node for mobile number input
  late final FocusNode mobileFocusNode;

  @override
  SignupLoginState build() {
    // Initialize text controller and focus node
    mobileController = TextEditingController();
    mobileFocusNode = FocusNode();

    // Listen to controller changes
    mobileController.addListener(_onMobileControllerChanged);

    // Clean up when provider is disposed
    ref.onDispose(() {
      mobileController.removeListener(_onMobileControllerChanged);
      mobileController.dispose();
      mobileFocusNode.dispose();
    });

    if (kDebugMode) {
      print('SignupLoginNotifier: Initialized');
    }

    return const SignupLoginState();
  }

  /// Handle mobile controller text changes
  void _onMobileControllerChanged() {
    final newPhoneNumber = mobileController.text;
    state = state.copyWith(phoneNumber: newPhoneNumber);

    if (kDebugMode) {
      print(
          'SignupLoginNotifier: Phone changed to "$newPhoneNumber" (length: ${newPhoneNumber.length})');
    }
  }

  /// Update phone number
  void updatePhoneNumber(String phoneNumber) {
    state = state.copyWith(phoneNumber: phoneNumber);

    // Update controller if different
    if (mobileController.text != phoneNumber) {
      mobileController.text = phoneNumber;
    }
  }

  /// Update country code
  void updateCountryCode(String countryCode) {
    state = state.copyWith(countryCode: countryCode);

    if (kDebugMode) {
      print('SignupLoginNotifier: Country code updated to: $countryCode');
    }
  }

  /// Set mobile field enabled state
  void setMobileEnabled(bool enabled) {
    state = state.copyWith(isMobileEnabled: enabled);
  }

  /// Clear error message
  void clearError() {
    state = state.clearError();
  }

  /// Reset login state (useful when returning from OTP view)
  void resetLoginState() {
    state = state.copyWith(
      isLoggingIn: false,
      clearErrorMessage: true,
    );
  }

  /// Validate phone number
  ///
  /// @param phone The phone number to validate
  /// @return True if valid, false otherwise
  bool validatePhoneNumber(String phone) {
    if (phone.isEmpty) {
      state = state.copyWith(errorMessage: 'Please enter your mobile number');
      return false;
    }

    if (!AuthValidators.isSaudiPhoneNumber(phone)) {
      state = state.copyWith(
          errorMessage: 'Please enter a valid Saudi mobile number');
      return false;
    }

    // Clear error if validation passes
    if (kDebugMode) {
      print('SignupLoginNotifier: Validation passed, clearing error');
    }
    state = state.clearError();
    if (kDebugMode) {
      print(
          'SignupLoginNotifier: After clearError, error: ${state.errorMessage}');
    }
    return true;
  }

  /// Initiate login process
  ///
  /// Validates input and starts the login flow
  Future<void> initiateLogin() async {
    final phone = state.phoneNumber.trim();

    if (kDebugMode) {
      print('SignupLoginNotifier: Initiating login for phone: "$phone"');
    }

    // Validate phone number
    if (!validatePhoneNumber(phone)) {
      if (kDebugMode) {
        print('SignupLoginNotifier: Phone validation failed');
      }
      return;
    }

    try {
      // Set loading state and clear any previous errors
      state = state.copyWith(isLoggingIn: true).clearError();

      // Format the complete phone number with country code
      final completePhoneNumber = '+${state.countryCode}$phone';

      if (kDebugMode) {
        print(
            'SignupLoginNotifier: Complete phone number: "$completePhoneNumber"');
      }

      // Get login use case
      final loginUserUseCase = ref.read(loginUserUseCaseProvider);

      // Initiate login through use case
      final result = await loginUserUseCase.initiateLogin(
        completePhoneNumber,
        '+${state.countryCode}',
      );

      if (result.isSuccess) {
        if (kDebugMode) {
          print('SignupLoginNotifier: Login initiated successfully');
          print(
              'SignupLoginNotifier: Using mobile number: $completePhoneNumber for both lookup and OTP');
          print(
              'SignupLoginNotifier: Setting isLoggingIn to false and clearing errors');
        }

        // Clear any previous errors
        state = state.clearError();

        if (kDebugMode) {
          print(
              'SignupLoginNotifier: State updated - isLoggingIn: ${state.isLoggingIn}, error: ${state.errorMessage}');
        }

        ToastCustom.successToast('تم إرسال رمز التحقق بنجاح!');
      } else {
        if (kDebugMode) {
          print(
              'SignupLoginNotifier: Login initiation failed - ${result.errorMessage}');
        }

        state = state.copyWith(errorMessage: result.errorMessage);
        ToastCustom.errorToast(result.errorMessage ?? 'فشل تسجيل الدخول');
      }
    } catch (e) {
      if (kDebugMode) {
        print('SignupLoginNotifier: Error during login - $e');
      }

      state = state.copyWith(
        errorMessage: 'An error occurred. Please try again.',
      );
      ToastCustom.errorToast('حدث خطأ. يرجى المحاولة مرة أخرى.');
    } finally {
      state = state.copyWith(isLoggingIn: false);
    }
  }

  /// Focus on the mobile input field
  void focusMobileInput() {
    mobileFocusNode.requestFocus();
  }

  /// Unfocus the mobile input field
  void unfocusMobileInput() {
    mobileFocusNode.unfocus();
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for phone number
@riverpod
String phoneNumber(PhoneNumberRef ref) {
  return ref.watch(signupLoginProvider).phoneNumber;
}

/// Provider for loading state
@riverpod
bool isLoggingIn(IsLoggingInRef ref) {
  return ref.watch(signupLoginProvider).isLoggingIn;
}

/// Provider for mobile field enabled state
@riverpod
bool isMobileEnabled(IsMobileEnabledRef ref) {
  return ref.watch(signupLoginProvider).isMobileEnabled;
}

/// Provider for country code
@riverpod
String countryCode(CountryCodeRef ref) {
  return ref.watch(signupLoginProvider).countryCode;
}

/// Provider for error message
@riverpod
String? errorMessage(ErrorMessageRef ref) {
  return ref.watch(signupLoginProvider).errorMessage;
}
