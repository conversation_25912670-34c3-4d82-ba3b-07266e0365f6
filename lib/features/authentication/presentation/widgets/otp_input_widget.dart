/// OTP Input Widget
///
/// Reusable widget for OTP code input in authentication screens
/// Provides consistent styling and behavior for OTP verification
library otp_input_widget;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// OTP Input Widget
///
/// Provides a row of input fields for OTP code entry
/// Automatically moves focus between fields and handles validation
class OtpInputWidget extends StatefulWidget {
  /// Number of OTP digits
  final int length;

  /// Callback when OTP is complete
  final Function(String) onCompleted;

  /// Callback when O<PERSON> changes
  final Function(String)? onChanged;

  /// Whether the widget is enabled
  final bool enabled;

  /// Creates an OtpInputWidget
  ///
  /// @param length Number of OTP digits
  /// @param onCompleted Callback when OTP is complete
  /// @param onChanged Callback when O<PERSON> changes
  /// @param enabled Whether the widget is enabled
  const OtpInputWidget({
    super.key,
    this.length = 4,
    required this.onCompleted,
    this.onChanged,
    this.enabled = true,
  });

  @override
  State<OtpInputWidget> createState() => _OtpInputWidgetState();
}

class _OtpInputWidgetState extends State<OtpInputWidget> {
  /// Controllers for each OTP digit field
  late List<TextEditingController> _controllers;

  /// Focus nodes for each OTP digit field
  late List<FocusNode> _focusNodes;

  @override
  void initState() {
    super.initState();

    // Initialize controllers and focus nodes
    _controllers = List.generate(
      widget.length,
      (index) => TextEditingController(),
    );

    _focusNodes = List.generate(
      widget.length,
      (index) => FocusNode(),
    );

    // Set up listeners for automatic focus movement
    for (int i = 0; i < widget.length; i++) {
      _controllers[i].addListener(() => _onTextChanged(i));
    }

    // Focus on first field when widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.enabled && _focusNodes.isNotEmpty) {
        FocusScope.of(context).requestFocus(_focusNodes[0]);
      }
    });
  }

  @override
  void dispose() {
    // Clean up controllers and focus nodes
    for (final controller in _controllers) {
      controller.dispose();
    }
    for (final focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  /// Handle text change in OTP field
  ///
  /// @param index Index of the field that changed
  void _onTextChanged(int index) {
    final text = _controllers[index].text;

    if (text.isNotEmpty) {
      // Move to next field if current field has text
      if (index < widget.length - 1) {
        FocusScope.of(context).requestFocus(_focusNodes[index + 1]);
      } else {
        // Unfocus if this is the last field
        FocusScope.of(context).unfocus();
      }
    }

    // Get current OTP value
    final otp = _getCurrentOtp();

    // Call onChanged callback
    widget.onChanged?.call(otp);

    // Call onCompleted if OTP is complete
    if (otp.length == widget.length) {
      widget.onCompleted(otp);
    }
  }

  /// Handle key press for backspace navigation
  ///
  /// @param index Index of the current field
  /// @param value Current value of the field
  void _onFieldChanged(int index, String value) {
    if (value.isEmpty && index > 0) {
      // Move to previous field if current field is empty
      FocusScope.of(context).requestFocus(_focusNodes[index - 1]);
    }
  }

  /// Get current OTP value from all fields
  ///
  /// @return Current OTP string
  String _getCurrentOtp() {
    return _controllers.map((controller) => controller.text).join();
  }

  /// Clear all OTP fields
  void clearOtp() {
    for (final controller in _controllers) {
      controller.clear();
    }
    if (widget.enabled && _focusNodes.isNotEmpty) {
      FocusScope.of(context).requestFocus(_focusNodes[0]);
    }
  }

  /// Build individual OTP input field
  ///
  /// @param index Index of the field
  /// @return Widget for the OTP input field
  Widget _buildOtpField(int index) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        border: Border.all(
          color: _focusNodes[index].hasFocus
              ? AppColors.primaryPurple
              : AppColors.greyMedium,
          width: _focusNodes[index].hasFocus ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: widget.enabled ? AppColors.whitePure : AppColors.greyLight,
      ),
      child: TextField(
        controller: _controllers[index],
        focusNode: _focusNodes[index],
        enabled: widget.enabled,
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: styleBlackLarge.copyWith(
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        decoration: const InputDecoration(
          counterText: '',
          border: InputBorder.none,
          contentPadding: EdgeInsets.zero,
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        onChanged: (value) => _onFieldChanged(index, value),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(
        widget.length,
        (index) => _buildOtpField(index),
      ),
    );
  }
}
