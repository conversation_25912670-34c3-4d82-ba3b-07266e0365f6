/// Phone Input Widget
///
/// Reusable widget for phone number input in authentication screens
/// Provides consistent styling and validation for phone number fields
library phone_input_widget;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Phone Input Widget
///
/// Provides a consistent phone number input field for authentication screens
/// Includes validation and formatting for Saudi phone numbers
class PhoneInputWidget extends StatelessWidget {
  /// Controller for the phone input field
  final TextEditingController controller;

  /// Focus node for the phone input field
  final FocusNode? focusNode;

  /// Label text for the input field
  final String label;

  /// Hint text for the input field
  final String hint;

  /// Whether the field is enabled
  final bool enabled;

  /// Callback when phone number changes
  final Function(PhoneNumber)? onChanged;

  /// Validation function
  final String? Function(PhoneNumber?)? validator;

  /// Initial country code
  final String initialCountryCode;

  /// Creates a PhoneInputWidget
  ///
  /// @param controller Controller for the phone input field
  /// @param focusNode Focus node for the phone input field
  /// @param label Label text for the input field
  /// @param hint Hint text for the input field
  /// @param enabled Whether the field is enabled
  /// @param onChanged Callback when phone number changes
  /// @param validator Validation function
  /// @param initialCountryCode Initial country code
  const PhoneInputWidget({
    super.key,
    required this.controller,
    this.focusNode,
    required this.label,
    required this.hint,
    this.enabled = true,
    this.onChanged,
    this.validator,
    this.initialCountryCode = 'SA',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 40),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label
          Text(
            label,
            style: styleBlackNormal.copyWith(
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),

          // Phone Input Field
          IntlPhoneField(
            controller: controller,
            focusNode: focusNode,
            enabled: enabled,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: styleGreyNormal.copyWith(
                fontSize: 14,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.greyMedium,
                  width: 1,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.greyMedium,
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.primaryPurple,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.red,
                  width: 1,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.red,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              filled: true,
              fillColor: enabled ? AppColors.whitePure : AppColors.greyLight,
            ),
            style: styleBlackNormal.copyWith(
              fontSize: 14,
            ),
            initialCountryCode: initialCountryCode,
            keyboardType: TextInputType.phone,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(10),
            ],
            onChanged: onChanged,
            validator: validator,
            showDropdownIcon: false,
            flagsButtonPadding: const EdgeInsets.symmetric(horizontal: 8),
            dropdownTextStyle: styleBlackNormal.copyWith(
              fontSize: 14,
            ),
            // countries: const ['SA'], // Restrict to Saudi Arabia only
          ),
        ],
      ),
    );
  }
}
