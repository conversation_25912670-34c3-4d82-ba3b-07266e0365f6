/// Mobile OTP View (Riverpod Version)
///
/// Riverpod version of the OTP verification screen following MVVM pattern
/// Demonstrates migration from GetX to Riverpod for OTP verification
library mobile_otp_view_riverpod;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/features/authentication/presentation/providers/mobile_otp_provider.dart';
import 'package:towasl/features/authentication/presentation/providers/auth_flow_provider.dart';
import 'package:towasl/l10n/app_localizations.dart';
import 'package:towasl/shared/widgets/bottom_action_bar.dart';

/// Mobile OTP View (Riverpod Version)
///
/// OTP verification screen that allows users to enter the OTP code
/// sent to their mobile number and verify their identity
/// Follows MVVM pattern with Riverpod providers instead of GetX
class MobileOtpView extends ConsumerStatefulWidget {
  /// Mobile number for OTP verification
  final String mobileNumber;

  /// Country code
  final String countryCode;

  /// Creates a MobileOtpView
  const MobileOtpView({
    super.key,
    required this.mobileNumber,
    this.countryCode = '966',
  });

  @override
  ConsumerState<MobileOtpView> createState() => _MobileOtpViewState();
}

/// State for the MobileOtpView
class _MobileOtpViewState extends ConsumerState<MobileOtpView> {
  bool _hasNavigated = false;

  @override
  void initState() {
    super.initState();

    // Initialize the provider with mobile number and country code
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(mobileOtpProvider.notifier).initialize(
            widget.mobileNumber,
            widget.countryCode,
          );

      // Focus on the first OTP input field when view opens
      ref.read(mobileOtpProvider.notifier).focusFirstInput();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Watch providers for reactive updates
    final mobileOtpState = ref.watch(mobileOtpProvider);
    final isVerifying = ref.watch(isVerifyingProvider);
    final isResending = ref.watch(isResendingProvider);

    // Listen for successful authentication and navigate accordingly
    ref.listen<AuthFlowState>(authFlowProvider, (previous, current) {
      // Check if authentication was successful and we haven't navigated yet
      if (previous?.userId == null &&
          current.userId != null &&
          !_hasNavigated) {
        _hasNavigated = true;

        // Navigate based on profile completion status
        _navigateBasedOnProfile(current);
      }

      // Reset navigation flag if there's an error
      if (current.errorMessage != null) {
        _hasNavigated = false;
      }
    });

    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      appBar: AppBar(
        backgroundColor: AppColors.whiteIvory,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.greyDark),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        title: Text(
          AppLocalizations.of(context).verifyMobileNumber,
          style: const TextStyle(
            color: AppColors.greyDark,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SafeArea(
        child: Stack(
          children: [
            // Main content wrapped in GestureDetector for tap outside handling
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                // Unfocus all OTP inputs when tapping outside
                ref.read(mobileOtpProvider.notifier).unfocusAllInputs();
              },
              child: _buildMainContent(mobileOtpState),
            ),

            // Loading overlay
            if (isVerifying || isResending)
              Container(
                color: Colors.black.withOpacity(0.3),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
          ],
        ),
      ),
      bottomNavigationBar: BottomActionBar(
        buttonText: AppLocalizations.of(context).verify,
        isEnabled: mobileOtpState.otpCode.length == 4,
        isLoading: isVerifying,
        onPressed: (mobileOtpState.otpCode.length == 4)
            ? () {
                ref.read(mobileOtpProvider.notifier).verifyOtp();
              }
            : null,
      ),
    );
  }

  /// Build the main content of the screen
  Widget _buildMainContent(MobileOtpState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 40),

          // Header section
          _buildHeader(state),

          const SizedBox(height: 40),

          // OTP input section
          _buildOtpInputSection(state),

          const SizedBox(height: 24),

          // Timer and resend section
          _buildTimerSection(state),

          const Spacer(),
        ],
      ),
    );
  }

  /// Build the header section
  Widget _buildHeader(MobileOtpState state) {
    return Column(
      children: [
        // OTP icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.primaryPurple.withOpacity(0.1),
            borderRadius: BorderRadius.circular(40),
          ),
          child: const Icon(
            Icons.sms_outlined,
            size: 40,
            color: AppColors.primaryPurple,
          ),
        ),

        const SizedBox(height: 24),

        // Title
        Text(
          AppLocalizations.of(context).enterVerificationCode,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.greyDark,
          ),
        ),

        const SizedBox(height: 12),

        // Subtitle with mobile number
        Column(
          children: [
            Text(
              AppLocalizations.of(context).weSentCodeTo,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.greyMedium,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 4),
            Directionality(
              textDirection: TextDirection.ltr,
              child: Text(
                '+${state.countryCode} ${_formatMobileNumber(state.mobileNumber)}',
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppColors.greyMedium,
                  height: 1.5,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build the OTP input section
  Widget _buildOtpInputSection(MobileOtpState state) {
    return Column(
      children: [
        // OTP input fields
        Directionality(
          textDirection: TextDirection.ltr,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(4, (index) {
              return SizedBox(
                width: 45,
                height: 55,
                child: TextField(
                  controller: ref
                      .read(mobileOtpProvider.notifier)
                      .otpControllers[index],
                  focusNode:
                      ref.read(mobileOtpProvider.notifier).focusNodes[index],
                  textAlign: TextAlign.center,
                  keyboardType: TextInputType.number,
                  maxLength: 1,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  decoration: InputDecoration(
                    counterText: '',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: AppColors.greyLight),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(
                          color: AppColors.primaryPurple, width: 2),
                    ),
                    filled: true,
                    fillColor: AppColors.whitePure,
                  ),
                  onChanged: (value) {
                    ref
                        .read(mobileOtpProvider.notifier)
                        .onOtpChanged(index, value);
                    // Clear error when user starts typing
                    if (state.errorMessage != null) {
                      ref.read(mobileOtpProvider.notifier).clearError();
                    }
                  },
                ),
              );
            }),
          ),
        ),

        // Error message
        if (state.errorMessage != null) ...[
          const SizedBox(height: 16),
          Text(
            state.errorMessage!,
            style: const TextStyle(
              color: Colors.red,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  /// Build the timer and resend section
  Widget _buildTimerSection(MobileOtpState state) {
    final remainingTime = ref.watch(remainingTimeProvider);
    final canResend = ref.watch(canResendProvider);
    final isResending = ref.watch(isResendingProvider);

    return Column(
      children: [
        if (!canResend) ...[
          Text(
            '${AppLocalizations.of(context).resendCodeIn} ${_formatTime(remainingTime)}',
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.greyMedium,
            ),
          ),
        ] else ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                AppLocalizations.of(context).didntReceiveCode,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.greyMedium,
                ),
              ),
              GestureDetector(
                onTap: isResending
                    ? null
                    : () {
                        ref.read(mobileOtpProvider.notifier).resendOtp();
                      },
                child: Text(
                  AppLocalizations.of(context).resend,
                  style: TextStyle(
                    fontSize: 14,
                    color: isResending
                        ? AppColors.greyMedium
                        : AppColors.primaryPurple,
                    fontWeight: FontWeight.w600,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Format mobile number for display
  String _formatMobileNumber(String mobileNumber) {
    if (mobileNumber.length >= 9) {
      return '${mobileNumber.substring(0, 2)} ${mobileNumber.substring(2, 5)} ${mobileNumber.substring(5)}';
    }
    return mobileNumber;
  }

  /// Format time in MM:SS format
  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// Navigate based on profile completion status
  void _navigateBasedOnProfile(AuthFlowState authFlowState) {
    // The auth flow provider handles navigation automatically
    // through the navigation provider, so we don't need to navigate here.
    // The auth flow provider will navigate to:
    // - Interests page if user has no interests
    // - Location page if user has no location
    // - Personal info page if user has no personal info
    // - Home page if profile is complete

    if (kDebugMode) {
      print(
          'MobileOtpView: Authentication successful, auth flow will handle navigation');
    }
  }
}
