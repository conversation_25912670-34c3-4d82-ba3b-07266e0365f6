/// Send OTP Use Case
///
/// Business logic for sending OTP to mobile number
/// <PERSON>les validation and delegates to repository for data operations
library send_otp_usecase;

import 'package:flutter/foundation.dart';
import 'package:towasl/features/authentication/domain/entities/login_request.dart';
import 'package:towasl/features/authentication/domain/entities/auth_response.dart';
import 'package:towasl/features/authentication/domain/repositories/auth_repository.dart';

/// Use case for sending OTP to mobile number
///
/// Encapsulates the business logic for initiating authentication
/// Validates input and coordinates with repository
class SendOtpUseCase {
  /// Authentication repository for data operations
  final AuthRepository _authRepository;

  /// Creates a SendOtpUseCase
  ///
  /// @param authRepository Repository for authentication operations
  const SendOtpUseCase(this._authRepository);

  /// Execute the send OTP operation
  ///
  /// Validates the login request and sends OTP to the mobile number
  ///
  /// @param loginRequest The login request containing mobile number and country code
  /// @return A Future that resolves to an AuthResponse
  Future<AuthResponse> execute(LoginRequest loginRequest) async {
    try {
      if (kDebugMode) {
        print('SendOtpUseCase: Executing send OTP for ${loginRequest.mobileNumber}');
      }

      // Validate mobile number format
      final validationError = _validateMobileNumber(loginRequest.mobileNumber);
      if (validationError != null) {
        if (kDebugMode) {
          print('SendOtpUseCase: Validation failed - $validationError');
        }
        return AuthResponse.failure(validationError);
      }

      // Validate country code
      if (loginRequest.countryCode.isEmpty) {
        if (kDebugMode) {
          print('SendOtpUseCase: Country code is required');
        }
        return AuthResponse.failure('Country code is required');
      }

      // Send OTP through repository
      final result = await _authRepository.sendOtp(loginRequest);

      if (kDebugMode) {
        print('SendOtpUseCase: Send OTP result - Success: ${result.isSuccess}');
      }

      return result;
    } catch (e) {
      if (kDebugMode) {
        print('SendOtpUseCase: Error sending OTP - $e');
      }
      return AuthResponse.failure('Failed to send OTP. Please try again.');
    }
  }

  /// Validate mobile number format
  ///
  /// @param mobileNumber The mobile number to validate
  /// @return Error message if invalid, null if valid
  String? _validateMobileNumber(String mobileNumber) {
    if (mobileNumber.isEmpty) {
      return 'Mobile number is required';
    }

    // Remove any non-digit characters
    final cleanNumber = mobileNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Check Saudi format (05xxxxxxxx)
    if (cleanNumber.length == 10 && cleanNumber.startsWith('05')) {
      return null;
    }

    // Check international format with country code (96605xxxxxxxx)
    if (cleanNumber.length == 13 && cleanNumber.startsWith('96605')) {
      return null;
    }

    return 'Invalid mobile number format';
  }
}
