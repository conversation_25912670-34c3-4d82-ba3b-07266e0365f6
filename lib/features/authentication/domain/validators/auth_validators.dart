/// Authentication Validators
///
/// Domain layer validation functions for authentication operations
/// Provides pure business logic validation without framework dependencies
library auth_validators;

/// Authentication validation functions
///
/// Contains validation logic for authentication-related data
/// Used by use cases to validate input before processing
class AuthValidators {
  /// Validates a Saudi phone number for authentication
  ///
  /// Checks that:
  /// - The phone number is not null or empty
  /// - The phone number matches the Saudi format (05xxxxxxxx)
  /// - The phone number is exactly 10 digits
  ///
  /// @param phoneNumber The phone number string to validate
  /// @return An error message if invalid, null otherwise
  static String? validateSaudiPhone(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return "Phone number is required";
    }

    // Remove any non-digit characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Saudi phone number format: 05xxxxxxxx (10 digits total)
    final regex = RegExp(r"^05[0-9]{8}$");
    
    if (!regex.hasMatch(cleanNumber)) {
      return 'Invalid Saudi phone number format';
    }
    
    return null;
  }

  /// Validates a raw phone number string
  ///
  /// @param phoneNumber The phone number string to validate
  /// @return An error message if invalid, null otherwise
  static String? validatePhoneString(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return "Phone number is required";
    }

    // Remove any non-digit characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check Saudi format
    if (cleanNumber.length == 10 && cleanNumber.startsWith('05')) {
      return null;
    }
    
    // Check international format with country code
    if (cleanNumber.length == 13 && cleanNumber.startsWith('96605')) {
      return null;
    }
    
    return 'Invalid phone number format';
  }

  /// Validates an OTP code
  ///
  /// Checks that the OTP is exactly 4 digits (based on current implementation)
  ///
  /// @param otp The OTP string to validate
  /// @return An error message if invalid, null otherwise
  static String? validateOTP(String? otp) {
    if (otp == null || otp.isEmpty) {
      return "OTP is required";
    }

    // Remove any whitespace
    final cleanOtp = otp.replaceAll(RegExp(r'\s'), '');
    
    // Check if it's exactly 4 digits (based on current implementation)
    if (cleanOtp.length != 4) {
      return "OTP must be 4 digits";
    }
    
    // Check if it contains only digits
    if (!RegExp(r'^\d{4}$').hasMatch(cleanOtp)) {
      return "OTP must contain only numbers";
    }
    
    return null;
  }

  /// Validates a verification code (generic)
  ///
  /// @param code The verification code to validate
  /// @param expectedLength The expected length of the code
  /// @return An error message if invalid, null otherwise
  static String? validateVerificationCode(String? code, int expectedLength) {
    if (code == null || code.isEmpty) {
      return "Verification code is required";
    }

    final cleanCode = code.replaceAll(RegExp(r'\s'), '');
    
    if (cleanCode.length != expectedLength) {
      return "Code must be $expectedLength digits";
    }
    
    if (!RegExp(r'^\d+$').hasMatch(cleanCode)) {
      return "Code must contain only numbers";
    }
    
    return null;
  }

  /// Validates country code
  ///
  /// @param countryCode The country code to validate
  /// @return An error message if invalid, null otherwise
  static String? validateCountryCode(String? countryCode) {
    if (countryCode == null || countryCode.isEmpty) {
      return "Country code is required";
    }

    // Remove any non-digit characters
    final cleanCode = countryCode.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check if it's a valid country code (1-4 digits)
    if (cleanCode.isEmpty || cleanCode.length > 4) {
      return "Invalid country code";
    }
    
    return null;
  }

  /// Formats a phone number for display in authentication
  ///
  /// @param phoneNumber The raw phone number
  /// @return The formatted phone number
  static String formatPhoneForDisplay(String phoneNumber) {
    // Remove any non-digit characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Format Saudi numbers
    if (cleanNumber.length == 10 && cleanNumber.startsWith('05')) {
      return '${cleanNumber.substring(0, 3)} ${cleanNumber.substring(3, 6)} ${cleanNumber.substring(6)}';
    }
    
    // Format international numbers
    if (cleanNumber.length == 13 && cleanNumber.startsWith('966')) {
      final localPart = cleanNumber.substring(3);
      return '+966 ${localPart.substring(0, 2)} ${localPart.substring(2, 5)} ${localPart.substring(5)}';
    }
    
    return phoneNumber;
  }

  /// Normalizes a phone number for storage/comparison
  ///
  /// @param phoneNumber The phone number to normalize
  /// @return The normalized phone number
  static String normalizePhoneNumber(String phoneNumber) {
    // Remove all non-digit characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Convert to standard format (05xxxxxxxx)
    if (cleanNumber.length == 13 && cleanNumber.startsWith('966')) {
      return '0${cleanNumber.substring(3)}';
    }
    
    if (cleanNumber.length == 10 && cleanNumber.startsWith('05')) {
      return cleanNumber;
    }
    
    return phoneNumber;
  }

  /// Checks if a phone number is in Saudi format
  ///
  /// @param phoneNumber The phone number to check
  /// @return True if it's a valid Saudi phone number
  static bool isSaudiPhoneNumber(String phoneNumber) {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    return cleanNumber.length == 10 && cleanNumber.startsWith('05');
  }

  /// Validates that two phone numbers match
  ///
  /// @param phone1 The first phone number
  /// @param phone2 The second phone number
  /// @return True if they match after normalization
  static bool phoneNumbersMatch(String phone1, String phone2) {
    final normalized1 = normalizePhoneNumber(phone1);
    final normalized2 = normalizePhoneNumber(phone2);
    return normalized1 == normalized2;
  }

  /// Generates a masked phone number for display
  ///
  /// Shows only the last 4 digits for privacy
  ///
  /// @param phoneNumber The phone number to mask
  /// @return The masked phone number
  static String maskPhoneNumber(String phoneNumber) {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanNumber.length >= 4) {
      final lastFour = cleanNumber.substring(cleanNumber.length - 4);
      final masked = '*' * (cleanNumber.length - 4);
      return '$masked$lastFour';
    }
    
    return phoneNumber;
  }

  /// Validates user ID format
  ///
  /// @param userId The user ID to validate
  /// @return An error message if invalid, null otherwise
  static String? validateUserId(String? userId) {
    if (userId == null || userId.isEmpty) {
      return "User ID is required";
    }

    // User ID should be at least 3 characters long
    if (userId.length < 3) {
      return "Invalid user ID format";
    }
    
    return null;
  }

  /// Validates session token format
  ///
  /// @param sessionToken The session token to validate
  /// @return An error message if invalid, null otherwise
  static String? validateSessionToken(String? sessionToken) {
    if (sessionToken == null || sessionToken.isEmpty) {
      return "Session token is required";
    }

    // Session token should be at least 16 characters long
    if (sessionToken.length < 16) {
      return "Invalid session token format";
    }
    
    return null;
  }
}
