// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$currentLatitudeHash() => r'b3cd2ad50fce600420e6562d968bea59866376e8';

/// Provider for current latitude
///
/// Copied from [currentLatitude].
@ProviderFor(currentLatitude)
final currentLatitudeProvider = Provider<double>.internal(
  currentLatitude,
  name: r'currentLatitudeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentLatitudeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentLatitudeRef = ProviderRef<double>;
String _$currentLongitudeHash() => r'1cbb1a2397e9a538a602bd84f532673b0fe63404';

/// Provider for current longitude
///
/// Copied from [currentLongitude].
@ProviderFor(currentLongitude)
final currentLongitudeProvider = Provider<double>.internal(
  currentLongitude,
  name: r'currentLongitudeProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentLongitudeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentLongitudeRef = ProviderRef<double>;
String _$currentCityHash() => r'5d082bb07954248fa0ce9a9de3fc2d5fc405a951';

/// Provider for current city
///
/// Copied from [currentCity].
@ProviderFor(currentCity)
final currentCityProvider = Provider<String>.internal(
  currentCity,
  name: r'currentCityProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$currentCityHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentCityRef = ProviderRef<String>;
String _$currentCountryHash() => r'00f63167ddaa148fac19db1ee14cf029095de2e4';

/// Provider for current country
///
/// Copied from [currentCountry].
@ProviderFor(currentCountry)
final currentCountryProvider = Provider<String>.internal(
  currentCountry,
  name: r'currentCountryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$currentCountryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CurrentCountryRef = ProviderRef<String>;
String _$isLocationLoadingHash() => r'c40565cfc07b84ac007745f951f0f1b15ee23994';

/// Provider for location loading state
///
/// Copied from [isLocationLoading].
@ProviderFor(isLocationLoading)
final isLocationLoadingProvider = Provider<bool>.internal(
  isLocationLoading,
  name: r'isLocationLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isLocationLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsLocationLoadingRef = ProviderRef<bool>;
String _$isLocationSavingHash() => r'536735d45eaf7008120b9d2ca03e87f282b224c6';

/// Provider for location saving state
///
/// Copied from [isLocationSaving].
@ProviderFor(isLocationSaving)
final isLocationSavingProvider = Provider<bool>.internal(
  isLocationSaving,
  name: r'isLocationSavingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isLocationSavingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsLocationSavingRef = ProviderRef<bool>;
String _$hasLocationPermissionHash() =>
    r'b8ccd3c432c31d4f2085ad2b0f8cea6c5eb0d1d5';

/// Provider for location permission status
///
/// Copied from [hasLocationPermission].
@ProviderFor(hasLocationPermission)
final hasLocationPermissionProvider = Provider<bool>.internal(
  hasLocationPermission,
  name: r'hasLocationPermissionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasLocationPermissionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HasLocationPermissionRef = ProviderRef<bool>;
String _$hasLocationDataHash() => r'03d3b204eb43ea7b1e27ec4a0565b53cfdd88312';

/// Provider for location data availability
///
/// Copied from [hasLocationData].
@ProviderFor(hasLocationData)
final hasLocationDataProvider = Provider<bool>.internal(
  hasLocationData,
  name: r'hasLocationDataProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$hasLocationDataHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef HasLocationDataRef = ProviderRef<bool>;
String _$locationErrorMessageHash() =>
    r'c147894dbe6754ca655f98db7117873589a56e70';

/// Provider for location error message
///
/// Copied from [locationErrorMessage].
@ProviderFor(locationErrorMessage)
final locationErrorMessageProvider = Provider<String?>.internal(
  locationErrorMessage,
  name: r'locationErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$locationErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef LocationErrorMessageRef = ProviderRef<String?>;
String _$locationHash() => r'3079b1e05aeffd15408cd58e28726575f4ea7102';

/// Location Notifier
///
/// Manages location state and business logic
/// Follows MVERPOD pattern with Riverpod state management
///
/// Copied from [Location].
@ProviderFor(Location)
final locationProvider = NotifierProvider<Location, LocationState>.internal(
  Location.new,
  name: r'locationProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$locationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Location = Notifier<LocationState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
