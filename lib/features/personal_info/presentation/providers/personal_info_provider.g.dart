// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'personal_info_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$userBirthdayYearHash() => r'46fff48dde4578e6757e24293d29d2728ec2c1cf';

/// Provider for birth year
///
/// Copied from [userBirthdayYear].
@ProviderFor(userBirthdayYear)
final userBirthdayYearProvider = AutoDisposeProvider<String>.internal(
  userBirthdayYear,
  name: r'userBirthdayYearProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userBirthdayYearHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserBirthdayYearRef = AutoDisposeProviderRef<String>;
String _$userGenderHash() => r'4fa17593f6ec316ccf78c717c7772959b9ae46a9';

/// Provider for gender
///
/// Copied from [userGender].
@ProviderFor(userGender)
final userGenderProvider = AutoDisposeProvider<String>.internal(
  userGender,
  name: r'userGenderProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userGenderHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserGenderRef = AutoDisposeProviderRef<String>;
String _$userNationalityHash() => r'b071a3778d887daab3d6ffc6678bad0afc7453d1';

/// Provider for nationality
///
/// Copied from [userNationality].
@ProviderFor(userNationality)
final userNationalityProvider = AutoDisposeProvider<String>.internal(
  userNationality,
  name: r'userNationalityProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$userNationalityHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef UserNationalityRef = AutoDisposeProviderRef<String>;
String _$isPersonalInfoLoadingHash() =>
    r'b7dba89a322c426572aac589d0623d0930468a6e';

/// Provider for personal info loading state
///
/// Copied from [isPersonalInfoLoading].
@ProviderFor(isPersonalInfoLoading)
final isPersonalInfoLoadingProvider = AutoDisposeProvider<bool>.internal(
  isPersonalInfoLoading,
  name: r'isPersonalInfoLoadingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isPersonalInfoLoadingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsPersonalInfoLoadingRef = AutoDisposeProviderRef<bool>;
String _$isPersonalInfoSavingHash() =>
    r'bf2ad7fbe4da955f2f7e9d9c0db30c5e423b5ebb';

/// Provider for personal info saving state
///
/// Copied from [isPersonalInfoSaving].
@ProviderFor(isPersonalInfoSaving)
final isPersonalInfoSavingProvider = AutoDisposeProvider<bool>.internal(
  isPersonalInfoSaving,
  name: r'isPersonalInfoSavingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isPersonalInfoSavingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsPersonalInfoSavingRef = AutoDisposeProviderRef<bool>;
String _$isPersonalInfoFormValidHash() =>
    r'1efed533be939326e681f159d81b877f71258e3a';

/// Provider for form validation
///
/// Copied from [isPersonalInfoFormValid].
@ProviderFor(isPersonalInfoFormValid)
final isPersonalInfoFormValidProvider = AutoDisposeProvider<bool>.internal(
  isPersonalInfoFormValid,
  name: r'isPersonalInfoFormValidProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isPersonalInfoFormValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsPersonalInfoFormValidRef = AutoDisposeProviderRef<bool>;
String _$personalInfoErrorMessageHash() =>
    r'6125f432588f3509614e69b8b48a66ad2f3c6126';

/// Provider for personal info error message
///
/// Copied from [personalInfoErrorMessage].
@ProviderFor(personalInfoErrorMessage)
final personalInfoErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  personalInfoErrorMessage,
  name: r'personalInfoErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$personalInfoErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef PersonalInfoErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$availableGendersHash() => r'6bf756fba8c3de3bcf147ee0ae4806aa7ab89207';

/// Available genders list
///
/// Copied from [availableGenders].
@ProviderFor(availableGenders)
final availableGendersProvider = AutoDisposeProvider<List<String>>.internal(
  availableGenders,
  name: r'availableGendersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$availableGendersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AvailableGendersRef = AutoDisposeProviderRef<List<String>>;
String _$availableNationalitiesHash() =>
    r'83f1e5c946a286dda8fece353868fd37c006765e';

/// Available nationalities list
///
/// Copied from [availableNationalities].
@ProviderFor(availableNationalities)
final availableNationalitiesProvider =
    AutoDisposeProvider<List<String>>.internal(
  availableNationalities,
  name: r'availableNationalitiesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$availableNationalitiesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AvailableNationalitiesRef = AutoDisposeProviderRef<List<String>>;
String _$personalInfoHash() => r'0a28990fa0f72d2e217eb9891059b92b452954ba';

/// Personal Info Notifier
///
/// Manages personal information state and business logic
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [PersonalInfo].
@ProviderFor(PersonalInfo)
final personalInfoProvider =
    AutoDisposeNotifierProvider<PersonalInfo, PersonalInfoState>.internal(
  PersonalInfo.new,
  name: r'personalInfoProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$personalInfoHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$PersonalInfo = AutoDisposeNotifier<PersonalInfoState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
