// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_form_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$formValidationStateHash() =>
    r'6a59c833cf66be59a28db1d3c3e4f12658c1af99';

/// Provider for form validation state
///
/// Copied from [formValidationState].
@ProviderFor(formValidationState)
final formValidationStateProvider =
    AutoDisposeProvider<FormValidationState>.internal(
  formValidationState,
  name: r'formValidationStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$formValidationStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FormValidationStateRef = AutoDisposeProviderRef<FormValidationState>;
String _$isFormSubmittingHash() => r'7bbacfcb2c0ada98fd87adbc36604f1efbb3794d';

/// Provider for form submission state
///
/// Copied from [isFormSubmitting].
@ProviderFor(isFormSubmitting)
final isFormSubmittingProvider = AutoDisposeProvider<bool>.internal(
  isFormSubmitting,
  name: r'isFormSubmittingProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$isFormSubmittingHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsFormSubmittingRef = AutoDisposeProviderRef<bool>;
String _$isFormDirtyHash() => r'9ae7b2844586acffc99c5606bffc57acb8feb522';

/// Provider for form dirty state
///
/// Copied from [isFormDirty].
@ProviderFor(isFormDirty)
final isFormDirtyProvider = AutoDisposeProvider<bool>.internal(
  isFormDirty,
  name: r'isFormDirtyProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isFormDirtyHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsFormDirtyRef = AutoDisposeProviderRef<bool>;
String _$isFormValidHash() => r'a1dafc5aa48858b619cbf833e2476af10453bcbe';

/// Provider for form validity
///
/// Copied from [isFormValid].
@ProviderFor(isFormValid)
final isFormValidProvider = AutoDisposeProvider<bool>.internal(
  isFormValid,
  name: r'isFormValidProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$isFormValidHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef IsFormValidRef = AutoDisposeProviderRef<bool>;
String _$canSubmitFormHash() => r'9ec21ca261209662b33f6b09367f27353f3cdabc';

/// Provider for form submission capability
///
/// Copied from [canSubmitForm].
@ProviderFor(canSubmitForm)
final canSubmitFormProvider = AutoDisposeProvider<bool>.internal(
  canSubmitForm,
  name: r'canSubmitFormProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$canSubmitFormHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef CanSubmitFormRef = AutoDisposeProviderRef<bool>;
String _$formFieldErrorsHash() => r'5ed4b2cac148a8b1c84039e4de677403208bb0e9';

/// Provider for field errors
///
/// Copied from [formFieldErrors].
@ProviderFor(formFieldErrors)
final formFieldErrorsProvider =
    AutoDisposeProvider<Map<String, String>>.internal(
  formFieldErrors,
  name: r'formFieldErrorsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$formFieldErrorsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FormFieldErrorsRef = AutoDisposeProviderRef<Map<String, String>>;
String _$formErrorMessageHash() => r'34f2e7fc33b348812877eba0c57629818eb2c884';

/// Provider for form error message
///
/// Copied from [formErrorMessage].
@ProviderFor(formErrorMessage)
final formErrorMessageProvider = AutoDisposeProvider<String?>.internal(
  formErrorMessage,
  name: r'formErrorMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$formErrorMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FormErrorMessageRef = AutoDisposeProviderRef<String?>;
String _$formSuccessMessageHash() =>
    r'1a1b0f8c3f0078d4045c94d4bdb4b6f86d1dcbf2';

/// Provider for form success message
///
/// Copied from [formSuccessMessage].
@ProviderFor(formSuccessMessage)
final formSuccessMessageProvider = AutoDisposeProvider<String?>.internal(
  formSuccessMessage,
  name: r'formSuccessMessageProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$formSuccessMessageHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FormSuccessMessageRef = AutoDisposeProviderRef<String?>;
String _$userFormHash() => r'fed67cf995783b340fb730248805fec67b100e4b';

/// User Form Notifier
///
/// Manages user form state and validation
/// Follows MVVM pattern with Riverpod state management
///
/// Copied from [UserForm].
@ProviderFor(UserForm)
final userFormProvider =
    AutoDisposeNotifierProvider<UserForm, UserFormState>.internal(
  UserForm.new,
  name: r'userFormProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$userFormHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$UserForm = AutoDisposeNotifier<UserFormState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
