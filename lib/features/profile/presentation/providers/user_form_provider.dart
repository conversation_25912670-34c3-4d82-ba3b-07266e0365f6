/// User Form Provider
///
/// Provides Riverpod state management for user form functionality
/// Replaces UserFormViewModel with Riverpod architecture
library user_form_provider;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towasl/features/profile/presentation/providers/user_provider.dart';
import 'package:towasl/shared/models/user_model.dart';

part 'user_form_provider.g.dart';

/// Form validation states
enum FormValidationState {
  valid,
  invalid,
  pending,
}

/// State class for user form management
class UserFormState {
  /// Form validation state
  final FormValidationState validationState;

  /// Whether form is being submitted
  final bool isSubmitting;

  /// Whether form data has been modified
  final bool isDirty;

  /// Form field errors
  final Map<String, String> fieldErrors;

  /// General form error message
  final String? errorMessage;

  /// Success message
  final String? successMessage;

  const UserFormState({
    this.validationState = FormValidationState.pending,
    this.isSubmitting = false,
    this.isDirty = false,
    this.fieldErrors = const {},
    this.errorMessage,
    this.successMessage,
  });

  UserFormState copyWith({
    FormValidationState? validationState,
    bool? isSubmitting,
    bool? isDirty,
    Map<String, String>? fieldErrors,
    String? errorMessage,
    String? successMessage,
  }) {
    return UserFormState(
      validationState: validationState ?? this.validationState,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      isDirty: isDirty ?? this.isDirty,
      fieldErrors: fieldErrors ?? this.fieldErrors,
      errorMessage: errorMessage ?? this.errorMessage,
      successMessage: successMessage ?? this.successMessage,
    );
  }

  /// Clear all messages
  UserFormState clearMessages() {
    return copyWith(
      errorMessage: null,
      successMessage: null,
    );
  }

  /// Clear field errors
  UserFormState clearFieldErrors() {
    return copyWith(fieldErrors: {});
  }

  /// Check if form is valid
  bool get isValid =>
      validationState == FormValidationState.valid && fieldErrors.isEmpty;

  /// Check if form can be submitted
  bool get canSubmit => isValid && !isSubmitting && isDirty;
}

/// User Form Notifier
///
/// Manages user form state and validation
/// Follows MVVM pattern with Riverpod state management
@riverpod
class UserForm extends _$UserForm {
  /// Form controllers
  late final TextEditingController nameController;
  late final TextEditingController birthdayYearController;
  late final TextEditingController mobileController;

  @override
  UserFormState build() {
    // Initialize form controllers
    nameController = TextEditingController();
    birthdayYearController = TextEditingController();
    mobileController = TextEditingController();

    // Add listeners for form changes
    nameController.addListener(_onFormChanged);
    birthdayYearController.addListener(_onFormChanged);
    mobileController.addListener(_onFormChanged);

    // Clean up when provider is disposed
    ref.onDispose(() {
      nameController.removeListener(_onFormChanged);
      birthdayYearController.removeListener(_onFormChanged);
      mobileController.removeListener(_onFormChanged);
      nameController.dispose();
      birthdayYearController.dispose();
      mobileController.dispose();
    });

    if (kDebugMode) {
      print('UserFormNotifier: Initialized');
    }

    return const UserFormState();
  }

  /// Handle form field changes
  void _onFormChanged() {
    state = state.copyWith(isDirty: true);
    _validateForm();
  }

  /// Load user data into form
  void loadUserData(UserModel user) {
    birthdayYearController.text = user.birthdayYear ?? '';
    mobileController.text = user.mobile ?? '';

    state = state.copyWith(isDirty: false);
    _validateForm();

    if (kDebugMode) {
      print('UserFormNotifier: User data loaded into form');
    }
  }

  /// Validate entire form
  void _validateForm() {
    final errors = <String, String>{};

    // Validate name
    final nameError = _validateName(nameController.text);
    if (nameError != null) {
      errors['name'] = nameError;
    }

    // Validate birthday year
    final birthdayError = _validateBirthdayYear(birthdayYearController.text);
    if (birthdayError != null) {
      errors['birthdayYear'] = birthdayError;
    }

    // Validate mobile
    final mobileError = _validateMobile(mobileController.text);
    if (mobileError != null) {
      errors['mobile'] = mobileError;
    }

    // Update validation state
    final validationState = errors.isEmpty
        ? FormValidationState.valid
        : FormValidationState.invalid;

    state = state.copyWith(
      validationState: validationState,
      fieldErrors: errors,
    );
  }

  /// Validate name field
  String? _validateName(String name) {
    if (name.trim().isEmpty) {
      return 'Name is required';
    }

    if (name.trim().length < 2) {
      return 'Name must be at least 2 characters';
    }

    if (name.trim().length > 50) {
      return 'Name must be less than 50 characters';
    }

    // Check for valid characters (letters, spaces, hyphens)
    if (!RegExp(r'^[a-zA-Z\u0600-\u06FF\s\-]+$').hasMatch(name.trim())) {
      return 'Name can only contain letters, spaces, and hyphens';
    }

    return null;
  }

  /// Validate birthday year field
  String? _validateBirthdayYear(String year) {
    if (year.trim().isEmpty) {
      return 'Birth year is required';
    }

    final yearInt = int.tryParse(year.trim());
    if (yearInt == null) {
      return 'Please enter a valid year';
    }

    final currentYear = DateTime.now().year;
    if (yearInt < currentYear - 90 || yearInt > currentYear - 13) {
      return 'Please enter a valid birth year (1950-${currentYear - 13})';
    }

    return null;
  }

  /// Validate mobile field
  String? _validateMobile(String mobile) {
    if (mobile.trim().isEmpty) {
      return 'Mobile number is required';
    }

    // Remove any non-digit characters for validation
    final cleanMobile = mobile.replaceAll(RegExp(r'[^\d]'), '');

    // Check Saudi format (05xxxxxxxx)
    if (cleanMobile.length == 10 && cleanMobile.startsWith('05')) {
      return null;
    }

    // Check international format with country code (96605xxxxxxxx)
    if (cleanMobile.length == 13 && cleanMobile.startsWith('96605')) {
      return null;
    }

    return 'Please enter a valid Saudi mobile number';
  }

  /// Validate specific field
  void validateField(String fieldName, String value) {
    String? error;

    switch (fieldName) {
      case 'name':
        error = _validateName(value);
        break;
      case 'birthdayYear':
        error = _validateBirthdayYear(value);
        break;
      case 'mobile':
        error = _validateMobile(value);
        break;
    }

    final updatedErrors = Map<String, String>.from(state.fieldErrors);
    if (error != null) {
      updatedErrors[fieldName] = error;
    } else {
      updatedErrors.remove(fieldName);
    }

    final validationState = updatedErrors.isEmpty
        ? FormValidationState.valid
        : FormValidationState.invalid;

    state = state.copyWith(
      fieldErrors: updatedErrors,
      validationState: validationState,
    );
  }

  /// Submit form
  Future<bool> submitForm() async {
    if (!state.canSubmit) {
      state = state.copyWith(
          errorMessage: 'Please fix form errors before submitting');
      return false;
    }

    if (kDebugMode) {
      print('UserFormNotifier: Submitting form');
    }

    try {
      state = state.copyWith(isSubmitting: true);

      // Create user model from form data
      final userModel = UserModel(
        birthdayYear: birthdayYearController.text.trim(),
        mobile: mobileController.text.trim(),
      );

      // Save through user provider
      final success =
          await ref.read(userProvider.notifier).saveUserData(userModel);

      if (success) {
        state = state.copyWith(
          isSubmitting: false,
          isDirty: false,
          successMessage: 'Profile updated successfully!',
        );

        if (kDebugMode) {
          print('UserFormNotifier: Form submitted successfully');
        }

        return true;
      } else {
        state = state.copyWith(
          isSubmitting: false,
          errorMessage: 'Failed to update profile',
        );

        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        errorMessage: 'An error occurred while updating profile',
      );

      if (kDebugMode) {
        print('UserFormNotifier: Error submitting form - $e');
      }

      return false;
    }
  }

  /// Reset form
  void resetForm() {
    nameController.clear();
    birthdayYearController.clear();
    mobileController.clear();

    state = const UserFormState();

    if (kDebugMode) {
      print('UserFormNotifier: Form reset');
    }
  }

  /// Clear messages
  void clearMessages() {
    state = state.clearMessages();
  }

  /// Clear field errors
  void clearFieldErrors() {
    state = state.clearFieldErrors();
  }
}

// ============================================================================
// CONVENIENCE PROVIDERS
// ============================================================================

/// Provider for form validation state
@riverpod
FormValidationState formValidationState(FormValidationStateRef ref) {
  return ref.watch(userFormProvider).validationState;
}

/// Provider for form submission state
@riverpod
bool isFormSubmitting(IsFormSubmittingRef ref) {
  return ref.watch(userFormProvider).isSubmitting;
}

/// Provider for form dirty state
@riverpod
bool isFormDirty(IsFormDirtyRef ref) {
  return ref.watch(userFormProvider).isDirty;
}

/// Provider for form validity
@riverpod
bool isFormValid(IsFormValidRef ref) {
  return ref.watch(userFormProvider).isValid;
}

/// Provider for form submission capability
@riverpod
bool canSubmitForm(CanSubmitFormRef ref) {
  return ref.watch(userFormProvider).canSubmit;
}

/// Provider for field errors
@riverpod
Map<String, String> formFieldErrors(FormFieldErrorsRef ref) {
  return ref.watch(userFormProvider).fieldErrors;
}

/// Provider for form error message
@riverpod
String? formErrorMessage(FormErrorMessageRef ref) {
  return ref.watch(userFormProvider).errorMessage;
}

/// Provider for form success message
@riverpod
String? formSuccessMessage(FormSuccessMessageRef ref) {
  return ref.watch(userFormProvider).successMessage;
}
