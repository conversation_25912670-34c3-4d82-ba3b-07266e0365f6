// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$storageServiceHash() => r'5239354c927df73466d0973a19c62d95b2f2ee8f';

/// Storage Service Provider
///
/// Provides persistent storage functionality using GetStorage
///
/// Copied from [storageService].
@ProviderFor(storageService)
final storageServiceProvider = AutoDisposeProvider<StorageService>.internal(
  storageService,
  name: r'storageServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$storageServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef StorageServiceRef = AutoDisposeProviderRef<StorageService>;
String _$firebaseServiceHash() => r'01ed0ac438afe11b1f6b6d41f1a0684e8951a11c';

/// Firebase Service Provider
///
/// Provides Firebase functionality for the application
///
/// Copied from [firebaseService].
@ProviderFor(firebaseService)
final firebaseServiceProvider = AutoDisposeProvider<FirebaseService>.internal(
  firebaseService,
  name: r'firebaseServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$firebaseServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef FirebaseServiceRef = AutoDisposeProviderRef<FirebaseService>;
String _$analyticsServiceHash() => r'2087e8e5a851f00830b43d31e8c5fa9148867a9b';

/// Analytics Service Provider
///
/// Provides Firebase Analytics functionality for the application
///
/// Copied from [analyticsService].
@ProviderFor(analyticsService)
final analyticsServiceProvider = AutoDisposeProvider<AnalyticsService>.internal(
  analyticsService,
  name: r'analyticsServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$analyticsServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef AnalyticsServiceRef = AutoDisposeProviderRef<AnalyticsService>;
String _$locationServiceHash() => r'6c4ddf91e28b248459625bb9f9fff0325a28bcbb';

/// Location Service Provider
///
/// Provides location functionality for the application
///
/// Copied from [locationService].
@ProviderFor(locationService)
final locationServiceProvider = AutoDisposeProvider<LocationService>.internal(
  locationService,
  name: r'locationServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$locationServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef LocationServiceRef = AutoDisposeProviderRef<LocationService>;
String _$versionServiceHash() => r'2604764ce33a563647fbeac4351326878eaf52b8';

/// Version Service Provider
///
/// Provides version checking functionality
///
/// Copied from [versionService].
@ProviderFor(versionService)
final versionServiceProvider = AutoDisposeProvider<VersionService>.internal(
  versionService,
  name: r'versionServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$versionServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef VersionServiceRef = AutoDisposeProviderRef<VersionService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
